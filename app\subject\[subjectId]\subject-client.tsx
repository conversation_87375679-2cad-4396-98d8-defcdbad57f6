'use client'

import { useMemo, useEffect, useState } from 'react';
import Link from 'next/link';
import { useSearchParams, useRouter } from 'next/navigation';
import { ArrowLeft, BookOpen, FileText, Home, GraduationCap } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useSubjectWithDetails } from '@/hooks/use-education-data';
import { isYearAllowedForExams } from '@/utils/examFilter';
import type { Lesson } from '@/data/types';

interface SubjectClientProps {
  subjectId: string;
}

export default function SubjectClient({ subjectId }: SubjectClientProps) {
  const { subject, year, lessons, isLoading, isError } = useSubjectWithDetails(subjectId);
  const searchParams = useSearchParams();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('exercises');

  // تحديد التبويب النشط من URL parameters
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['exercises', 'homeworks', 'summaries', 'exams'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // تصنيف الدروس حسب النوع باستخدام useMemo للأداء الأفضل
  const { exercises, homeworks, summaries, exams, isExamLevelAllowed } = useMemo(() => {
    const exercises = lessons.filter(lesson => lesson.content_type === 'exercise');
    const homeworks = lessons.filter(lesson => lesson.content_type === 'homework');
    const summaries = lessons.filter(lesson => lesson.content_type === 'summary');
    const exams = lessons.filter(lesson => lesson.content_type === 'exam');

    // تحديد ما إذا كان هذا المستوى مسموح له بعرض الامتحانات
    const yearId = year?.id || '';
    const isExamLevelAllowed = isYearAllowedForExams(yearId);

    return {
      exercises,
      homeworks,
      summaries,
      exams,
      isExamLevelAllowed
    };
  }, [lessons, year]);

  // دالة لتحديث التبويب النشط وURL
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const url = new URL(window.location.href);
    url.searchParams.set('tab', value);
    router.replace(url.pathname + url.search, { scroll: false });
  };



  const renderLessonCard = (lesson: Lesson, type: string) => {
    const getIcon = () => {
      switch (type) {
        case 'exercise': return <BookOpen className="h-6 w-6" />;
        case 'homework': return <Home className="h-6 w-6" />;
        case 'summary': return <FileText className="h-6 w-6" />;
        case 'exam': return <GraduationCap className="h-6 w-6" />;
        default: return <BookOpen className="h-6 w-6" />;
      }
    };

    const getRoute = () => {
      const baseRoute = (() => {
        switch (type) {
          case 'exercise': return `/lesson/${lesson.id}`;
          case 'homework': return `/homework/${lesson.id}`;
          case 'summary': return `/summary/${lesson.id}`;
          case 'exam': return `/exam/${lesson.id}`;
          default: return `/lesson/${lesson.id}`;
        }
      })();

      // إضافة معامل العودة للحفاظ على التبويب النشط
      const returnTab = (() => {
        switch (type) {
          case 'exercise': return 'exercises';
          case 'homework': return 'homeworks';
          case 'summary': return 'summaries';
          case 'exam': return 'exams';
          default: return 'exercises';
        }
      })();

      return `${baseRoute}?returnTo=${encodeURIComponent(`/subject/${subjectId}?tab=${returnTab}`)}`;
    };

    return (
      <Link
        key={lesson.id}
        href={getRoute()}
        className="level-card transition-all duration-300 hover:shadow-md hover:scale-[1.02]"
        dir="rtl"
      >
        <div className="flex items-center gap-4 rtl-flex-row-reverse">
          <div className="flex-1 text-right flex items-center">
            <div className="text-lg font-bold arabic-heading">{lesson.title}</div>
          </div>
          <div className="p-2 bg-primary/10 rounded-lg text-primary">
            {getIcon()}
          </div>
        </div>
        <div className="bg-muted rounded-full p-2 transition-all duration-300 hover:bg-primary/20">
          <ArrowLeft className="h-5 w-5 text-primary transition-transform hover:translate-x-1" />
        </div>
      </Link>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="container mx-auto px-4 py-12 flex items-center justify-center">
          <div className="text-center" dir="rtl">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-lg arabic-text">جاري تحميل البيانات...</p>
          </div>
        </div>
        <div className="mt-auto">
          <Footer />
        </div>
      </div>
    );
  }

  if (isError || !subject) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <div className="container mx-auto px-4 py-12 flex items-center justify-center">
          <div className="text-center" dir="rtl">
            <h1 className="text-2xl font-bold text-destructive mb-4 arabic-heading">خطأ</h1>
            <p className="text-lg mb-4 arabic-text">حدث خطأ أثناء تحميل البيانات أو لم يتم العثور على المادة الدراسية</p>
            <Link
              href="/levels"
              className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors rtl-button"
              dir="rtl"
            >
              العودة إلى المستويات
              <ArrowLeft className="h-4 w-4 ml-2 rtl:rotate-180" />
            </Link>
          </div>
        </div>
        <div className="mt-auto">
          <Footer />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <div className="container mx-auto px-4 py-12">
        {/* Header Section */}
        <div className="text-center mb-12" dir="rtl">
          <div className="mb-4">
            <h1 className="text-3xl md:text-4xl font-bold text-primary text-center">{subject.name}</h1>
          </div>
          {subject.description && (
            <div className="mb-4">
              <p className="text-lg text-foreground text-center arabic-text max-w-3xl mx-auto">
                {subject.description}
              </p>
            </div>
          )}
          {year && (
            <p className="text-muted-foreground text-center">
              {year.name}
            </p>
          )}
        </div>

        {/* Breadcrumb */}
        <div className="mb-8" dir="rtl">
          <nav className="rtl-breadcrumb">
            <Link href="/levels" className="hover:text-primary transition-colors arabic-text">
              المستويات
            </Link>
            <span className="rtl-breadcrumb-separator">/</span>
            {year && (
              <>
                <Link href={`/year/${year.id}`} className="hover:text-primary transition-colors arabic-text">
                  {year.name}
                </Link>
                <span className="rtl-breadcrumb-separator">/</span>
              </>
            )}
            <span className="text-foreground arabic-text">{subject.name}</span>
          </nav>
        </div>

        {/* Content Tabs */}
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className={`grid w-full ${isExamLevelAllowed ? 'grid-cols-4' : 'grid-cols-3'}`}>
            <TabsTrigger value="exercises">التمارين ({exercises.length})</TabsTrigger>
            <TabsTrigger value="homeworks">الفروض ({homeworks.length})</TabsTrigger>
            <TabsTrigger value="summaries">الملخصات ({summaries.length})</TabsTrigger>
            {isExamLevelAllowed && (
              <TabsTrigger value="exams">الامتحانات ({exams.length})</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="exercises" className="mt-6">
            {exercises.length > 0 ? (
              <div className="space-y-6">
                {exercises.map(lesson => renderLessonCard(lesson, 'exercise'))}
              </div>
            ) : (
              <div className="text-center py-12" dir="rtl">
                <BookOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2 arabic-heading">لا توجد تمارين</h3>
                <p className="text-muted-foreground arabic-text">لم يتم إضافة تمارين لهذه المادة بعد</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="homeworks" className="mt-6">
            {homeworks.length > 0 ? (
              <div className="space-y-6">
                {homeworks.map(lesson => renderLessonCard(lesson, 'homework'))}
              </div>
            ) : (
              <div className="text-center py-12" dir="rtl">
                <Home className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2 arabic-heading">لا توجد فروض</h3>
                <p className="text-muted-foreground arabic-text">لم يتم إضافة فروض لهذه المادة بعد</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="summaries" className="mt-6">
            {summaries.length > 0 ? (
              <div className="space-y-6">
                {summaries.map(lesson => renderLessonCard(lesson, 'summary'))}
              </div>
            ) : (
              <div className="text-center py-12" dir="rtl">
                <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2 arabic-heading">لا توجد ملخصات</h3>
                <p className="text-muted-foreground arabic-text">لم يتم إضافة ملخصات لهذه المادة بعد</p>
              </div>
            )}
          </TabsContent>

          {isExamLevelAllowed && (
            <TabsContent value="exams" className="mt-6">
              {exams.length > 0 ? (
                <div className="space-y-6">
                  {exams.map(lesson => renderLessonCard(lesson, 'exam'))}
                </div>
              ) : (
                <div className="text-center py-12" dir="rtl">
                  <GraduationCap className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2 arabic-heading">لا توجد امتحانات</h3>
                  <p className="text-muted-foreground arabic-text">
                    لم يتم إضافة امتحانات لهذه المادة بعد
                  </p>
                </div>
              )}
            </TabsContent>
          )}
        </Tabs>
      </div>

      <div className="mt-auto">
        <Footer />
      </div>
    </div>
  );
}
