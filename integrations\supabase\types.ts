export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      exams: {
        Row: {
          id: string
          lesson_id: string | null
          hint: string | null
          exercise_image_url: string | null
          solution_image_url: string | null
        }
        Insert: {
          id: string
          lesson_id?: string | null
          hint?: string | null
          exercise_image_url?: string | null
          solution_image_url?: string | null
        }
        Update: {
          id?: string
          lesson_id?: string | null
          hint?: string | null
          exercise_image_url?: string | null
          solution_image_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "exams_lesson_id_fkey"
            columns: ["lesson_id"]
            isOneToOne: false
            referencedRelation: "lessons"
            referencedColumns: ["id"]
          },
        ]
      }
      summaries: {
        Row: {
          exercise_image_url: string | null
          hint: string | null
          id: string
          lesson_id: string | null
        }
        Insert: {
          exercise_image_url?: string | null
          hint?: string | null
          id: string
          lesson_id?: string | null
        }
        Update: {
          exercise_image_url?: string | null
          hint?: string | null
          id?: string
          lesson_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "summaries_lesson_id_fkey"
            columns: ["lesson_id"]
            isOneToOne: false
            referencedRelation: "lessons"
            referencedColumns: ["id"]
          },
        ]
      }
      exercises: {
        Row: {
          exercise_image_url: string | null
          hint: string | null
          id: string
          lesson_id: string | null
          options: string[] | null
          question: string | null
          solution: string | null
          solution_image_url: string | null
          student_input_type: string
        }
        Insert: {
          exercise_image_url?: string | null
          hint?: string | null
          id: string
          lesson_id?: string | null
          options?: string[] | null
          question?: string | null
          solution?: string | null
          solution_image_url?: string | null
          student_input_type: string
        }
        Update: {
          exercise_image_url?: string | null
          hint?: string | null
          id?: string
          lesson_id?: string | null
          options?: string[] | null
          question?: string | null
          solution?: string | null
          solution_image_url?: string | null
          student_input_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "exercises_lesson_id_fkey"
            columns: ["lesson_id"]
            isOneToOne: false
            referencedRelation: "lessons"
            referencedColumns: ["id"]
          },
        ]
      }
      homeworks: {
        Row: {
          exercise_image_url: string | null
          hint: string | null
          id: string
          lesson_id: string | null
          options: string[] | null
          question: string | null
          solution: string | null
          solution_image_url: string | null
          student_input_type: string
        }
        Insert: {
          exercise_image_url?: string | null
          hint?: string | null
          id: string
          lesson_id?: string | null
          options?: string[] | null
          question?: string | null
          solution?: string | null
          solution_image_url?: string | null
          student_input_type: string
        }
        Update: {
          exercise_image_url?: string | null
          hint?: string | null
          id?: string
          lesson_id?: string | null
          options?: string[] | null
          question?: string | null
          solution?: string | null
          solution_image_url?: string | null
          student_input_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "homeworks_lesson_id_fkey"
            columns: ["lesson_id"]
            isOneToOne: false
            referencedRelation: "lessons"
            referencedColumns: ["id"]
          },
        ]
      }
      lessons: {
        Row: {
          content_type: string | null
          description: string
          id: string
          subject_id: string | null
          title: string
        }
        Insert: {
          content_type?: string | null
          description: string
          id: string
          subject_id?: string | null
          title: string
        }
        Update: {
          content_type?: string | null
          description?: string
          id?: string
          subject_id?: string | null
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "lessons_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      levels: {
        Row: {
          description: string
          id: string
          name: string
          years: string[] | null
          display_order: number | null
        }
        Insert: {
          description: string
          id: string
          name: string
          years?: string[] | null
          display_order?: number | null
        }
        Update: {
          description?: string
          id?: string
          name?: string
          years?: string[] | null
          display_order?: number | null
        }
        Relationships: []
      }
      subjects: {
        Row: {
          icon: string
          id: string
          lessons: string[] | null
          name: string
          year_id: string | null
        }
        Insert: {
          icon: string
          id: string
          lessons?: string[] | null
          name: string
          year_id?: string | null
        }
        Update: {
          icon?: string
          id?: string
          lessons?: string[] | null
          name?: string
          year_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subjects_year_id_fkey"
            columns: ["year_id"]
            isOneToOne: false
            referencedRelation: "years"
            referencedColumns: ["id"]
          },
        ]
      }
      years: {
        Row: {
          id: string
          level_id: string | null
          name: string
          subjects: string[] | null
          description: string | null
        }
        Insert: {
          id: string
          level_id?: string | null
          name: string
          subjects?: string[] | null
          description?: string | null
        }
        Update: {
          id?: string
          level_id?: string | null
          name?: string
          subjects?: string[] | null
          description?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "years_level_id_fkey"
            columns: ["level_id"]
            isOneToOne: false
            referencedRelation: "levels"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
