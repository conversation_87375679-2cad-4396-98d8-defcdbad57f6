-- إضافة حقل description إلى جدول subjects
-- يجب تشغيل هذا الاستعلام في Supabase SQL Editor

-- إضافة العمود الجديد
ALTER TABLE subjects 
ADD COLUMN description TEXT;

-- إض<PERSON><PERSON>ة تعليق على العمود
COMMENT ON COLUMN subjects.description IS 'وصف المادة الدراسية';

-- تحديث بعض المواد بأوصاف تجريبية (اختياري)
-- يمكنك حذف هذا الجزء إذا كنت تريد إضافة الأوصاف يدوياً من لوحة التحكم

-- مثال على تحديث الأوصاف (قم بتعديل هذه القيم حسب احتياجاتك)
/*
UPDATE subjects 
SET description = 'تعلم أساسيات الرياضيات والعمليات الحسابية المختلفة مع تمارين تطبيقية شاملة'
WHERE name = 'الرياضيات';

UPDATE subjects 
SET description = 'استكشف قواعد اللغة العربية والأدب والتعبير مع نصوص متنوعة وتمارين تفاعلية'
WHERE name = 'اللغة العربية';

UPDATE subjects 
SET description = 'تعلم أساسيات اللغة الفرنسية من القواعد والمفردات إلى المحادثة والكتابة'
WHERE name = 'اللغة الفرنسية';

UPDATE subjects 
SET description = 'اكتشف العالم من حولك من خلال دراسة الجغرافيا والتاريخ والثقافات المختلفة'
WHERE name = 'الاجتماعيات';

UPDATE subjects 
SET description = 'استكشف عجائب العلوم الطبيعية من الفيزياء والكيمياء إلى علوم الحياة'
WHERE name = 'العلوم';
*/
